import { assertEquals, assertExists } from "@std/assert";
import { CBRPNKSet } from "./CBRPNKSet.ts";

Deno.test("CBRPNKSet", async (t) => {
  await t.step("should have correct label", () => {
    assertEquals(CBRPNKSet.label, "CBR+PNK");
  });

  await t.step("should not be translatable", () => {
    assertEquals(CBRPNKSet.translatable, false);
  });

  await t.step("should have correct column number", () => {
    const set = new CBRPNKSet();
    assertEquals(set.columnNumber, 9);
  });

  await t.step("should have correct elements configuration", () => {
    const set = new CBRPNKSet();
    const elements = set.elements;
    
    // Should have 6 dice + 1 group roll button = 7 elements
    assertEquals(elements.length, 7);
    
    // First 6 elements should be dice
    for (let i = 0; i < 6; i++) {
      const element = elements[i];
      assertEquals(element.type, "die");
      assertEquals(element.name, "d6");
      assertEquals(element.size, 6);
      assertEquals(element.single, true);
      assertEquals(element.singleThrowDisabled, true);
      assertExists(element.groupRoll);
      assertEquals(element.groupRoll.optional, true);
    }
    
    // Last element should be group roll button
    const groupButton = elements[6];
    assertEquals(groupButton.type, "group-button");
    assertEquals(groupButton.buttonText, "roll");
    assertEquals(groupButton.resultConsolidation.strategy, "individual");
    assertEquals(groupButton.resultSort, "descending");
    assertEquals(groupButton.span, 3);
  });

  await t.step("should have a valid tag", () => {
    assertExists(CBRPNKSet.tag);
    assertEquals(typeof CBRPNKSet.tag, "string");
  });
});
