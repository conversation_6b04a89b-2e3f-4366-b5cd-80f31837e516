import {
  DiceGroupRollButtonSettings,
  DiceGroupSettings,
  DieSettings,
} from "types/models.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { PolyDie } from "../playables/dice/die/PolyhedricDie.ts";
import { DiceGroupRollButton } from "./DiceGroupRollButton.ts";
import { DiceSet } from "./DiceSet.ts";

const groupRollId = `${DiceGroupRollButton.tag}-cbrpnk`;

export class CBRPNKSet extends DiceSet {
  static override get label(): string {
    return `CBR+PNK™️`;
  }
  static override get translatable(): boolean {
    return false;
  }
  override get columnNumber(): number {
    return 9;
  }
  override get elements(): (
    | DieSettings
    | DiceGroupSettings
    | DiceGroupRollButtonSettings
    | string
  )[] {
    // Create multiple d6 dice for building dice pools
    const dice: DieSettings[] = Array.from({ length: 6 }, () => ({
      type: "die",
      tag: PolyDie.tag,
      name: "d6",
      color: "yellow",
      size: 6,
      single: true,
      singleThrowDisabled: true,
      groupRoll: {
        id: groupRollId,
        optional: true,
      },
    }));

    const groupRollButton: DiceGroupRollButtonSettings = {
      type: "group-button",
      id: groupRollId,
      buttonText: "roll",
      resultConsolidation: { strategy: "individual" },
      resultSort: "descending",
      span: 3,
    };

    return [
      ...dice,
      groupRollButton,
    ];
  }
}
safeCustomDefine(CBRPNKSet);
